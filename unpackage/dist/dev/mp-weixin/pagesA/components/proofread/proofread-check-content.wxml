<view class="data-v-10667d72"><view class="cu-card margin-sm radius shadow bg-white flex padding-sm data-v-10667d72"><view class="flex-sub-0 margin-right-sm data-v-10667d72"><block wx:if="{{form.imgHead}}"><u-lazy-load vue-id="01cbe89f-1" image="{{$root.g0}}" width="120" height="160" border-radius="4" class="data-v-10667d72" bind:__l="__l"></u-lazy-load></block></view><view class="flex-sub text-content data-v-10667d72"><view class="flex justify-between align-center data-v-10667d72"><view class="data-v-10667d72"><view class="flex justify-start align-center data-v-10667d72"><view class="text-df text-bold text-black data-v-10667d72">{{form.name}}</view><block wx:if="{{form.sex==1}}"><view class="text-sm text-blue margin-left-lg data-v-10667d72">男<text class="cuIcon-male data-v-10667d72"></text></view></block><block wx:if="{{form.sex==2}}"><view class="text-sm text-pink margin-left-lg data-v-10667d72">女<text class="cuIcon-female data-v-10667d72"></text></view></block></view><view class="text-sm text-gray margin-top-xs data-v-10667d72"><block wx:if="{{form.phone}}"><text class="cuIcon-phone margin-right-xs data-v-10667d72"></text></block>{{''+form.phone+''}}</view></view><view class="text-right data-v-10667d72"><view class="text-sm text-gray data-v-10667d72">护理日期</view><view class="text-df text-bold data-v-10667d72" style="{{'color:'+(colors)+';'}}">{{form.workDate}}</view></view></view><view class="text-xs text-gray text-cut margin-top-xs data-v-10667d72">{{form.address}}</view><block wx:if="{{form.attendantName}}"><view class="margin-top-xs data-v-10667d72"><text class="text-sm data-v-10667d72" style="{{'color:'+(colors)+';'}}">{{"("+form.groupName+")"}}</text><text class="text-sm text-gray data-v-10667d72">{{"护理员："+form.attendantName}}</text></view></block><block wx:if="{{form.idcard}}"><view class="flex align-center margin-top-xs data-v-10667d72"><text class="text-xs text-gray margin-right-xs data-v-10667d72">证件号码：</text><text class="text-xs text-black data-v-10667d72">{{form.idcard}}</text></view></block><block wx:if="{{form.isManual&&form.proofreadErrorRemark}}"><view class="margin-top-xs data-v-10667d72"><text class="text-sm text-gray data-v-10667d72">校对结果：</text><text class="cu-tag sm bg-orange light radius data-v-10667d72">{{''+form.proofreadErrorRemark+''}}</text></view></block></view></view><block wx:if="{{joinedDatas}}"><view class="cu-card margin-sm radius shadow s-gray bg-white data-v-10667d72"><file-data-item vue-id="01cbe89f-2" detail="{{joinedDatas}}" colors="{{colors}}" only-view="{{true}}" class="data-v-10667d72" bind:__l="__l"></file-data-item></view></block><block wx:else><view class="cu-card margin-sm radius shadow s-gray bg-white padding-sm text-center data-v-10667d72">暂无资料</view></block><view class="cu-card margin-sm padding-lr-sm radius shadow bg-white data-v-10667d72"><block wx:for="{{errorTypeOptions}}" wx:for-item="option" wx:for-index="__i0__" wx:key="*this"><view class="error-type-item data-v-10667d72"><view class="flex justify-between align-center data-v-10667d72"><view class="text-df text-black data-v-10667d72" style="width:90px;">{{option+"："}}</view><u-radio-group style="display:flex;" vue-id="{{'01cbe89f-3-'+__i0__}}" value="{{errorTypeStates[option]}}" placement="row" data-event-opts="{{[['^change',[['updateErrorType',['$0','$event'],[[['errorTypeOptions','',__i0__]]]]]]]}}" bind:change="__e" class="data-v-10667d72" bind:__l="__l" vue-slots="{{['default']}}"><u-radio vue-id="{{('01cbe89f-4-'+__i0__)+','+('01cbe89f-3-'+__i0__)}}" name="{{false}}" activeColor="{{colors}}" label="正确" customStyle="{{({marginRight:'40rpx',padding:'10rpx'})}}" class="data-v-10667d72" bind:__l="__l"></u-radio><u-radio vue-id="{{('01cbe89f-5-'+__i0__)+','+('01cbe89f-3-'+__i0__)}}" name="{{true}}" activeColor="#f56c6c" label="错误" customStyle="{{({padding:'10rpx'})}}" class="data-v-10667d72" bind:__l="__l"></u-radio></u-radio-group></view></view></block></view><view class="cu-card margin-sm radius shadow bg-white data-v-10667d72"><view class="cu-bar bg-white solid-bottom data-v-10667d72"><view class="action data-v-10667d72"><text class="cuIcon-title text-orange data-v-10667d72"></text>服务项目</view><view class="action data-v-10667d72"><u-button vue-id="01cbe89f-6" type="primary" color="{{colors}}" text="选择项目" size="small" shape="circle" data-event-opts="{{[['^click',[['showProjectPopup']]]]}}" bind:click="__e" class="data-v-10667d72" bind:__l="__l"></u-button></view></view><block wx:if="{{$root.g1}}"><view class="data-v-10667d72"><block wx:for="{{orderProjects}}" wx:for-item="project" wx:for-index="index" wx:key="index"><project-item vue-id="{{'01cbe89f-7-'+index}}" project="{{project}}" colors="{{colors}}" show-remove="{{true}}" data-event-opts="{{[['^remove',[['removeProject']]]]}}" bind:remove="__e" class="data-v-10667d72" bind:__l="__l"></project-item></block></view></block><block wx:if="{{$root.g2}}"><view class="text-center padding-xl data-v-10667d72"><text class="text-gray data-v-10667d72">无服务项目</text></view></block></view></view>