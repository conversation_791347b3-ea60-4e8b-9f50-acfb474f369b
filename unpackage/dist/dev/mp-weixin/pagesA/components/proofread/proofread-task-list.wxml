<ut-page vue-id="7883bf2d-1" class="data-v-4ace3e0e" bind:__l="__l" vue-slots="{{['default']}}"><ut-top class="top-warp data-v-4ace3e0e" vue-id="{{('7883bf2d-2')+','+('7883bf2d-1')}}" bg-color="#fff" data-event-opts="{{[['^topHeight',[['getHeight']]]]}}" bind:topHeight="__e" bind:__l="__l" vue-slots="{{['default']}}"><f-navbar vue-id="{{('7883bf2d-3')+','+('7883bf2d-2')}}" fontColor="#fff" bgColor="{{colors}}" title="{{title}}" navbarType="1" class="data-v-4ace3e0e" bind:__l="__l"></f-navbar><view class="padding-top-sm padding-lr-sm flex align-center data-v-4ace3e0e" style="{{'background-color:'+('#fff')+';'}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="u-border margin-right-sm round padding-tb-sm padding-lr-lg data-v-4ace3e0e" bindtap="__e"><view class="text-grey text-bold data-v-4ace3e0e">{{month}}</view></view><u-input bind:input="__e" vue-id="{{('7883bf2d-4')+','+('7883bf2d-2')}}" prefixIcon="search" placeholder="输入客户或护理员姓名" shape="circle" border="surround" clearable="{{true}}" value="{{search.key}}" data-event-opts="{{[['^input',[['__set_model',['$0','key','$event',[]],['search']]]]]}}" class="data-v-4ace3e0e" bind:__l="__l" vue-slots="{{['suffix']}}"><view slot="suffix" class="data-v-4ace3e0e"><u-button vue-id="{{('7883bf2d-5')+','+('7883bf2d-4')}}" text="搜索" type="success" size="mini" data-event-opts="{{[['^click',[['onSearch']]]]}}" bind:click="__e" class="data-v-4ace3e0e" bind:__l="__l"></u-button></view></u-input></view><view class="filter-bar data-v-4ace3e0e"><u-tabs vue-id="{{('7883bf2d-6')+','+('7883bf2d-2')}}" list="{{filterTabs}}" activeColor="{{colors}}" value="{{currentFilter}}" data-event-opts="{{[['^change',[['onFilterChange']]],['^input',[['__set_model',['','currentFilter','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-4ace3e0e" bind:__l="__l"></u-tabs></view></ut-top><mescroll-body vue-id="{{('7883bf2d-7')+','+('7883bf2d-1')}}" top="{{topWrapHeight+'px'}}" top-margin="{{-topWrapHeight+'px'}}" bottom="20" up="{{upOption}}" safearea="{{true}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^down',[['downCallback']]],['^up',[['upCallback']]],['^emptyclick',[['emptyClick']]]]}}" bind:init="__e" bind:down="__e" bind:up="__e" bind:emptyclick="__e" class="data-v-4ace3e0e vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goToCheck',['$0'],[[['dataList','',index]]]]]]]}}" bindtap="__e" class="data-v-4ace3e0e"><view class="item-box data-v-4ace3e0e"><view class="head-image data-v-4ace3e0e"><block wx:if="{{item.$orig.imgHead}}"><u-lazy-load vue-id="{{('7883bf2d-8-'+index)+','+('7883bf2d-7')}}" image="{{item.g0}}" width="120" height="160" border-radius="4" class="data-v-4ace3e0e" bind:__l="__l"></u-lazy-load></block><block wx:else><text class="cuIcon-people data-v-4ace3e0e"></text></block></view><view class="content text-content data-v-4ace3e0e"><view class="flex justify-between align-center data-v-4ace3e0e"><view class="data-v-4ace3e0e"><view class="flex justify-start align-center flex-wrap data-v-4ace3e0e"><view class="text-df text-bold margin-right-xs data-v-4ace3e0e">{{item.$orig.name}}</view><view class="{{['data-v-4ace3e0e',(true)?'cu-tag sm radius light':'',(item.$orig.proofreadError)?'bg-orange':'',(!item.$orig.proofreadError&&item.$orig.isManual)?'bg-green':'',(!item.$orig.proofreadError&&!item.$orig.isManual)?'bg-blue':'']}}">{{''+item.m0+''}}</view></view><view class="text-sm text-gray margin-top-xs data-v-4ace3e0e">{{'手机号：'+item.$orig.phone+''}}</view><view class="text-sm text-gray margin-top-xs data-v-4ace3e0e">{{'护理日期：'+item.$orig.workDate+''}}</view></view></view><view class="text-sm text-gray margin-top-xs data-v-4ace3e0e">{{'护理员：'+item.$orig.attendantName+"（"+item.$orig.groupName+'）'}}</view><block wx:if="{{item.$orig.isManual}}"><view class="text-sm text-gray margin-top-xs data-v-4ace3e0e">{{'校对人：'+item.$orig.lastManualUserName+''}}</view><view class="text-sm text-gray margin-top-xs data-v-4ace3e0e">{{'校对时间：'+item.$orig.lastManualTime+''}}</view></block></view></view></view></block></mescroll-body><u-datetime-picker vue-id="{{('7883bf2d-9')+','+('7883bf2d-1')}}" show="{{showMonthPicker}}" mode="year-month" title="选择月份" closeOnClickOverlay="{{true}}" value="{{monthValue}}" data-event-opts="{{[['^confirm',[['onMonthConfirm']]],['^close',[['e1']]],['^cancel',[['e2']]],['^input',[['__set_model',['','monthValue','$event',[]]]]]]}}" bind:confirm="__e" bind:close="__e" bind:cancel="__e" bind:input="__e" class="data-v-4ace3e0e" bind:__l="__l"></u-datetime-picker></ut-page>