{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-check-content.vue?3806", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-check-content.vue?38b3", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-check-content.vue?5748", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-check-content.vue?9816", "uni-app:///pagesA/components/proofread/proofread-check-content.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-check-content.vue?2edf", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-check-content.vue?fee6"], "names": ["name", "components", "ProjectItem", "FileDataItem", "props", "form", "type", "required", "colors", "default", "joinedDatas", "orderProjects", "errorTypeOptions", "errorTypeStates", "options", "styleIsolation", "methods", "updateErrorType", "showProjectPopup", "removeProject"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgJ;AAChJ;AAC2E;AACL;AACsC;;;AAG5G;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,6FAAM;AACR,EAAE,8GAAM;AACR,EAAE,uHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kHAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,aAAa,yUAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAAmtB,CAAgB,qpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC8HvuB;EACAA;EACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAG;IACA;IACAC;MACAJ;MACAG;IACA;IACAE;MACAL;MACAG;QAAA;MAAA;IACA;IACAG;MACAN;MACAG;QAAA;MAAA;IACA;IACAI;MACAP;MACAG;QAAA;MAAA;IACA;EACA;EACAK;IACAC;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC5KA;AAAA;AAAA;AAAA;AAAk2C,CAAgB,4qCAAG,EAAC,C;;;;;;;;;;;ACAt3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/components/proofread/proofread-check-content.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./proofread-check-content.vue?vue&type=template&id=10667d72&scoped=true&\"\nvar renderjs\nimport script from \"./proofread-check-content.vue?vue&type=script&lang=js&\"\nexport * from \"./proofread-check-content.vue?vue&type=script&lang=js&\"\nimport style0 from \"./proofread-check-content.vue?vue&type=style&index=0&id=10667d72&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"10667d72\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/components/proofread/proofread-check-content.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-check-content.vue?vue&type=template&id=10667d72&scoped=true&\"", "var components\ntry {\n  components = {\n    uLazyLoad: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-lazy-load/u-lazy-load\" */ \"@/components/uview-ui/components/u-lazy-load/u-lazy-load.vue\"\n      )\n    },\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-radio-group/u-radio-group\" */ \"@/components/uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-radio/u-radio\" */ \"@/components/uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.form.imgHead ? _vm.$tools.showImg(_vm.form.imgHead) : null\n  var g1 = _vm.orderProjects && _vm.orderProjects.length\n  var g2 = !_vm.form.projects || !_vm.form.projects.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-check-content.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-check-content.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <!-- 客户信息卡片 -->\n        <view class=\"cu-card margin-sm radius shadow bg-white flex padding-sm\">\n            <view class=\"flex-sub-0 margin-right-sm\">\n                <u-lazy-load v-if=\"form.imgHead\"\n                    :image=\"$tools.showImg(form.imgHead)\"\n                    width=\"120\"\n                    height=\"160\"\n                    border-radius=\"4\" />\n            </view>\n            <view class=\"flex-sub text-content\">\n                <view class=\"flex justify-between align-center\">\n                    <view>\n                        <view class=\"flex justify-start align-center\">\n                            <view class=\"text-df text-bold text-black\">{{ form.name }}</view>\n                            <view class=\"text-sm text-blue margin-left-lg\" v-if=\"form.sex == 1\">男\n                                <text class=\"cuIcon-male\" />\n                            </view>\n                            <view class=\"text-sm text-pink margin-left-lg\" v-if=\"form.sex == 2\">女\n                                <text class=\"cuIcon-female\" />\n                            </view>\n                        </view>\n                        <view class=\"text-sm text-gray margin-top-xs\">\n                            <text v-if=\"form.phone\" class=\"cuIcon-phone margin-right-xs\" />\n                            {{ form.phone }}\n                        </view>\n                    </view>\n                    <view class=\"text-right\">\n                        <view class=\"text-sm text-gray\">护理日期</view>\n                        <view class=\"text-df text-bold\" :style=\"{ color: colors }\">{{ form.workDate }}</view>\n                    </view>\n                </view>\n\n                <view class=\"text-xs text-gray text-cut margin-top-xs\">{{ form.address }}</view>\n                <view v-if=\"form.attendantName\" class=\"margin-top-xs\">\n                    <text class=\"text-sm\" :style=\"{ color: colors }\">({{ form.groupName }})</text>\n                    <text class=\"text-sm text-gray\">护理员：{{ form.attendantName }}</text>\n                </view>\n\n                <view v-if=\"form.idcard\" class=\"flex align-center margin-top-xs\">\n                    <text class=\"text-xs text-gray margin-right-xs\">证件号码：</text>\n                    <text class=\"text-xs text-black\">{{ form.idcard }}</text>\n                </view>\n\n                <view v-if=\"form.isManual && form.proofreadErrorRemark\" class=\"margin-top-xs\">\n                    <text class=\"text-sm text-gray\">校对结果：</text>\n                    <text class=\"cu-tag sm bg-orange light radius\">\n                        {{ form.proofreadErrorRemark }}\n                    </text>\n                </view>\n            </view>\n        </view>\n\n        <!-- 文件资料区域 -->\n        <view v-if=\"joinedDatas\" class=\"cu-card margin-sm radius shadow s-gray bg-white\">\n            <file-data-item :detail=\"joinedDatas\" :colors=\"colors\" :only-view=\"true\"></file-data-item>\n        </view>\n        <view v-else class=\"cu-card margin-sm radius shadow s-gray bg-white padding-sm text-center\">暂无资料</view>\n\n        <!-- 错误类型选择区域 -->\n        <view class=\"cu-card margin-sm padding-lr-sm radius shadow bg-white\">\n            <view v-for=\"option in errorTypeOptions\" :key=\"option\" class=\"error-type-item\">\n                <view class=\"flex justify-between align-center\">\n                    <view class=\"text-df text-black\" style=\"width: 90px;\">{{ option }}：</view>\n                    <u-radio-group\n                        :value=\"errorTypeStates[option]\"\n                        placement=\"row\"\n                        @change=\"updateErrorType(option, $event)\"\n                        style=\"display: flex\">\n                        <u-radio\n                            :name=\"false\"\n                            :activeColor=\"colors\"\n                            label=\"正确\"\n                            :customStyle=\"{ marginRight: '40rpx', padding: '10rpx' }\"\n                        ></u-radio>\n                        <u-radio\n                            :name=\"true\"\n                            activeColor=\"#f56c6c\"\n                            label=\"错误\"\n                            :customStyle=\"{ padding: '10rpx' }\"\n                        ></u-radio>\n                    </u-radio-group>\n                </view>\n            </view>\n        </view>\n\n        <!-- 服务项目列表区域 -->\n        <view class=\"cu-card margin-sm radius shadow bg-white\">\n            <view class=\"cu-bar bg-white solid-bottom\">\n                <view class=\"action\">\n                    <text class=\"cuIcon-title text-orange\"></text>\n                    服务项目\n                </view>\n                <view class=\"action\">\n                    <u-button\n                        type=\"primary\"\n                        :color=\"colors\"\n                        @click=\"showProjectPopup\"\n                        text=\"选择项目\"\n                        size=\"small\"\n                        shape=\"circle\"\n                    ></u-button>\n                </view>\n            </view>\n            <view v-if=\"orderProjects && orderProjects.length\">\n                <project-item\n                    v-for=\"(project, index) in orderProjects\"\n                    :key=\"index\"\n                    :project=\"project\"\n                    :colors=\"colors\"\n                    :show-remove=\"true\"\n                    @remove=\"removeProject\"\n                />\n            </view>\n            <view v-if=\"!form.projects || !form.projects.length\" class=\"text-center padding-xl\">\n                <text class=\"text-gray\">无服务项目</text>\n            </view>\n        </view>\n    </view>\n</template>\n\n<script>\nimport ProjectItem from '@/pagesA/components/project-item.vue'\nimport FileDataItem from '@/pagesA/components/file-data-item.vue'\n\nexport default {\n    name: 'ProofreadCheckContent',\n    components: {\n        ProjectItem,\n        FileDataItem,\n    },\n    props: {\n        form: {\n            type: Object,\n            required: true,\n        },\n        colors: {\n            type: String,\n            default: '',\n        },\n        joinedDatas: {\n            type: Object,\n            default: null,\n        },\n        orderProjects: {\n            type: Array,\n            default: () => [],\n        },\n        errorTypeOptions: {\n            type: Array,\n            default: () => [],\n        },\n        errorTypeStates: {\n            type: Object,\n            default: () => ({}),\n        },\n    },\n    options: {\n        styleIsolation: 'shared',\n    },\n    methods: {\n        updateErrorType(option, event) {\n            this.$emit('update-error-type', option, event)\n        },\n        showProjectPopup() {\n            this.$emit('show-project-popup')\n        },\n        removeProject(project) {\n            this.$emit('remove-project', project)\n        },\n    },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.cuIcon-people {\n  font-size: 116rpx;\n  color: gray;\n  border: 1rpx solid #ccc;\n  width: 116rpx;\n  height: 156rpx;\n  line-height: 156rpx;\n  border-radius: 6rpx;\n  display: block;\n}\n\n.error-type-item {\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n\n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n/deep/ .u-radio {\n  min-height: 88rpx;\n  display: flex;\n  align-items: center;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-check-content.vue?vue&type=style&index=0&id=10667d72&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-check-content.vue?vue&type=style&index=0&id=10667d72&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754362109948\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}