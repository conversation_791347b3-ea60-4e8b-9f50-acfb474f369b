{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/nurse-proofread/check.vue?aa02", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/nurse-proofread/check.vue?66bb", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/nurse-proofread/check.vue?adb3", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/nurse-proofread/check.vue?83f5", "uni-app:///pagesA/nurse/nurse-proofread/check.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "ProofreadTaskCheck", "options", "styleIsolation", "data", "workId", "month", "colors", "onLoad", "onShow"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;;;AAGpD;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAisB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACartB;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;EACA;EACAC;IACA;MAAAF;IAAA;EACA;AACA;AAAA,2B", "file": "pagesA/nurse/nurse-proofread/check.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/nurse/nurse-proofread/check.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./check.vue?vue&type=template&id=05be8fed&\"\nvar renderjs\nimport script from \"./check.vue?vue&type=script&lang=js&\"\nexport * from \"./check.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/nurse/nurse-proofread/check.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./check.vue?vue&type=template&id=05be8fed&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./check.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./check.vue?vue&type=script&lang=js&\"", "<template>\n    <proofread-task-check\n        api-path=\"mang/nurse\"\n        title=\"服务项目校对\"\n        :work-id=\"workId\"\n        :month=\"month\"\n        :colors=\"colors\"\n    />\n</template>\n\n<script>\nimport ProofreadTaskCheck from '@/pagesA/components/proofread/proofread-task-check.vue'\n\nconst app = getApp()\n\nexport default {\n    components: {\n        ProofreadTaskCheck,\n    },\n    options: {\n        styleIsolation: 'shared',\n    },\n    data() {\n        return {\n            workId: '',\n            month: '',\n            colors: '',\n        }\n    },\n    onLoad(options) {\n        this.workId = options.workId ?? ''\n        this.month = options.month\n    },\n    onShow() {\n        this.setData({ colors: app.globalData.newColor })\n    },\n}\n</script>\n"], "sourceRoot": ""}